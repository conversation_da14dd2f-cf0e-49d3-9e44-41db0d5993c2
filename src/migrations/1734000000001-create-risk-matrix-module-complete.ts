import { MigrationInterface, QueryRunner } from 'typeorm';

export class createRiskMatrixModuleComplete1734000000001 implements MigrationInterface {
  name = 'createRiskMatrixModuleComplete1734000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum type for risk matrix status
    await queryRunner.query(`
      CREATE TYPE "risk_matrix_master_status_enum" AS ENUM('active', 'inactive')
    `);

    // Create risk_matrix_master table
    await queryRunner.query(`
      CREATE TABLE "risk_matrix_master" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "matrixCode" varchar(50) NOT NULL,
        "matrixName" varchar(255),
        "rows" integer NOT NULL,
        "columns" integer NOT NULL,
        "rowsName" text,
        "columnsName" text,
        "status" "risk_matrix_master_status_enum" NOT NULL DEFAULT 'active',
        "companyId" uuid NOT NULL,
        "createdUserId" uuid,
        "updatedUserId" uuid,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted" boolean NOT NULL DEFAULT false,
        CONSTRAINT "PK_risk_matrix_master" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for risk_matrix_master
    await queryRunner.query(`
      CREATE INDEX "idx_risk_matrix_master_companyId" 
      ON "risk_matrix_master"("companyId") 
      WHERE deleted = false
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_risk_matrix_master_code_companyId" 
      ON "risk_matrix_master"("matrixCode", "companyId") 
      WHERE deleted = false
    `);

    // Create risk_value_mapping table
    await queryRunner.query(`
      CREATE TABLE "risk_value_mapping" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "riskMatrixId" uuid NOT NULL,
        "valueId" varchar(100),
        "valueKey" varchar(100) NOT NULL,
        "valueLabel" varchar(255) NOT NULL,
        "sortOrder" integer,
        "color" varchar(7),
        "description" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted" boolean NOT NULL DEFAULT false,
        CONSTRAINT "PK_risk_value_mapping" PRIMARY KEY ("id")
      )
    `);

    // Create unique index for risk_value_mapping
    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_risk_value_mapping_matrix" 
      ON "risk_value_mapping"("riskMatrixId", "valueKey") 
      WHERE deleted = false
    `);

    // Create risk_matrix_cell table
    await queryRunner.query(`
      CREATE TABLE "risk_matrix_cell" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "riskMatrixId" uuid NOT NULL,
        "riskValueMappingId" uuid,
        "cellPositions" text,
        "rowNames" text,
        "columnNames" text,
        "riskLevel" varchar(50),
        "backgroundColor" varchar(7),
        "description" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted" boolean NOT NULL DEFAULT false,
        CONSTRAINT "PK_risk_matrix_cell" PRIMARY KEY ("id")
      )
    `);

    // Create index for risk_matrix_cell
    await queryRunner.query(`
      CREATE INDEX "idx_risk_matrix_cell_value_mapping" 
      ON "risk_matrix_cell"("riskMatrixId", "riskValueMappingId") 
      WHERE deleted = false
    `);

    // Create risk_level_mapping table
    await queryRunner.query(`
      CREATE TABLE "risk_level_mapping" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "riskMatrixId" uuid NOT NULL,
        "riskValueMappingIds" text,
        "levelLabel" varchar(255) NOT NULL,
        "minValue" integer,
        "maxValue" integer,
        "color" varchar(7),
        "sortOrder" integer NOT NULL DEFAULT 0,
        "description" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted" boolean NOT NULL DEFAULT false,
        CONSTRAINT "PK_risk_level_mapping" PRIMARY KEY ("id")
      )
    `);

    // Create unique index for risk_level_mapping
    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_risk_level_mapping_matrix" 
      ON "risk_level_mapping"("riskMatrixId", "levelLabel") 
      WHERE deleted = false
    `);

    // Create junction table for many-to-many relationship between risk_level_mapping and risk_value_mapping
    await queryRunner.query(`
      CREATE TABLE "risk_level_mapping_value_mappings" (
        "risk_level_mapping_id" uuid NOT NULL,
        "risk_value_mapping_id" uuid NOT NULL,
        CONSTRAINT "PK_risk_level_mapping_value_mappings" PRIMARY KEY ("risk_level_mapping_id", "risk_value_mapping_id")
      )
    `);

    // Create indexes for junction table
    await queryRunner.query(`
      CREATE INDEX "IDX_risk_level_mapping_value_mappings_level" 
      ON "risk_level_mapping_value_mappings" ("risk_level_mapping_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_risk_level_mapping_value_mappings_value" 
      ON "risk_level_mapping_value_mappings" ("risk_value_mapping_id")
    `);

    // Add foreign key constraints
    
    // risk_matrix_master foreign keys
    await queryRunner.query(`
      ALTER TABLE "risk_matrix_master" 
      ADD CONSTRAINT "FK_risk_matrix_master_company" 
      FOREIGN KEY ("companyId") REFERENCES "company"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "risk_matrix_master" 
      ADD CONSTRAINT "FK_risk_matrix_master_created_user" 
      FOREIGN KEY ("createdUserId") REFERENCES "user"("id") 
      ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "risk_matrix_master" 
      ADD CONSTRAINT "FK_risk_matrix_master_updated_user" 
      FOREIGN KEY ("updatedUserId") REFERENCES "user"("id") 
      ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    // risk_value_mapping foreign keys
    await queryRunner.query(`
      ALTER TABLE "risk_value_mapping" 
      ADD CONSTRAINT "FK_risk_value_mapping_matrix" 
      FOREIGN KEY ("riskMatrixId") REFERENCES "risk_matrix_master"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // risk_matrix_cell foreign keys
    await queryRunner.query(`
      ALTER TABLE "risk_matrix_cell" 
      ADD CONSTRAINT "FK_risk_matrix_cell_matrix" 
      FOREIGN KEY ("riskMatrixId") REFERENCES "risk_matrix_master"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "risk_matrix_cell" 
      ADD CONSTRAINT "FK_risk_matrix_cell_value_mapping" 
      FOREIGN KEY ("riskValueMappingId") REFERENCES "risk_value_mapping"("id") 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // risk_level_mapping foreign keys
    await queryRunner.query(`
      ALTER TABLE "risk_level_mapping" 
      ADD CONSTRAINT "FK_risk_level_mapping_matrix" 
      FOREIGN KEY ("riskMatrixId") REFERENCES "risk_matrix_master"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Junction table foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "risk_level_mapping_value_mappings" 
      ADD CONSTRAINT "FK_risk_level_mapping_value_mappings_level" 
      FOREIGN KEY ("risk_level_mapping_id") REFERENCES "risk_level_mapping"("id") 
      ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "risk_level_mapping_value_mappings"
      ADD CONSTRAINT "FK_risk_level_mapping_value_mappings_value"
      FOREIGN KEY ("risk_value_mapping_id") REFERENCES "risk_value_mapping"("id")
      ON DELETE CASCADE ON UPDATE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop junction table foreign key constraints and indexes
    await queryRunner.query(`DROP INDEX "IDX_risk_level_mapping_value_mappings_value"`);
    await queryRunner.query(`DROP INDEX "IDX_risk_level_mapping_value_mappings_level"`);
    await queryRunner.query(`ALTER TABLE "risk_level_mapping_value_mappings" DROP CONSTRAINT "FK_risk_level_mapping_value_mappings_value"`);
    await queryRunner.query(`ALTER TABLE "risk_level_mapping_value_mappings" DROP CONSTRAINT "FK_risk_level_mapping_value_mappings_level"`);

    // Drop junction table
    await queryRunner.query(`DROP TABLE "risk_level_mapping_value_mappings"`);

    // Drop foreign key constraints (in reverse order)
    await queryRunner.query(`ALTER TABLE "risk_level_mapping" DROP CONSTRAINT "FK_risk_level_mapping_matrix"`);
    await queryRunner.query(`ALTER TABLE "risk_matrix_cell" DROP CONSTRAINT "FK_risk_matrix_cell_value_mapping"`);
    await queryRunner.query(`ALTER TABLE "risk_matrix_cell" DROP CONSTRAINT "FK_risk_matrix_cell_matrix"`);
    await queryRunner.query(`ALTER TABLE "risk_value_mapping" DROP CONSTRAINT "FK_risk_value_mapping_matrix"`);
    await queryRunner.query(`ALTER TABLE "risk_matrix_master" DROP CONSTRAINT "FK_risk_matrix_master_updated_user"`);
    await queryRunner.query(`ALTER TABLE "risk_matrix_master" DROP CONSTRAINT "FK_risk_matrix_master_created_user"`);
    await queryRunner.query(`ALTER TABLE "risk_matrix_master" DROP CONSTRAINT "FK_risk_matrix_master_company"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "idx_risk_level_mapping_matrix"`);
    await queryRunner.query(`DROP INDEX "idx_risk_matrix_cell_value_mapping"`);
    await queryRunner.query(`DROP INDEX "idx_risk_value_mapping_matrix"`);
    await queryRunner.query(`DROP INDEX "idx_risk_matrix_master_code_companyId"`);
    await queryRunner.query(`DROP INDEX "idx_risk_matrix_master_companyId"`);

    // Drop tables (in reverse order of creation)
    await queryRunner.query(`DROP TABLE "risk_level_mapping"`);
    await queryRunner.query(`DROP TABLE "risk_matrix_cell"`);
    await queryRunner.query(`DROP TABLE "risk_value_mapping"`);
    await queryRunner.query(`DROP TABLE "risk_matrix_master"`);

    // Drop enum types
    await queryRunner.query(`DROP TYPE "risk_matrix_master_status_enum"`);
  }
}
