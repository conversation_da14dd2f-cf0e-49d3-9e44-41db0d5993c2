import { MigrationInterface, QueryRunner } from 'typeorm';

export class enhanceRiskMatrixMaster1734000000000 implements MigrationInterface {
  name = 'enhanceRiskMatrixMaster1734000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns to risk_matrix_master table
    await queryRunner.query(`
      ALTER TABLE "risk_matrix_master" 
      ADD COLUMN "matrix_code" varchar(50) NOT NULL DEFAULT '',
      ADD COLUMN "matrix_name" varchar(255)
    `);

    // Create unique index for matrix_code and companyId
    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_risk_matrix_master_code_companyId" 
      ON "risk_matrix_master"("matrix_code", "company_id") 
      WHERE deleted = false
    `);

    // Create risk_matrix_cell table
    await queryRunner.query(`
      CREATE TABLE "risk_matrix_cell" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "risk_matrix_id" uuid NOT NULL,
        "risk_value_mapping_id" uuid,
        "cell_positions" text,
        "risk_level" varchar(50),
        "background_color" varchar(7),
        "description" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted" boolean NOT NULL DEFAULT false,
        CONSTRAINT "PK_risk_matrix_cell" PRIMARY KEY ("id")
      )
    `);

    // Create index for risk_matrix_cell value mapping
    await queryRunner.query(`
      CREATE INDEX "idx_risk_matrix_cell_value_mapping"
      ON "risk_matrix_cell"("risk_matrix_id", "risk_value_mapping_id")
      WHERE deleted = false
    `);

    // Create foreign key constraints for risk_matrix_cell
    await queryRunner.query(`
      ALTER TABLE "risk_matrix_cell"
      ADD CONSTRAINT "FK_risk_matrix_cell_matrix"
      FOREIGN KEY ("risk_matrix_id") REFERENCES "risk_matrix_master"("id")
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "risk_matrix_cell"
      ADD CONSTRAINT "FK_risk_matrix_cell_value_mapping"
      FOREIGN KEY ("risk_value_mapping_id") REFERENCES "risk_value_mapping"("id")
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Create risk_value_mapping table
    await queryRunner.query(`
      CREATE TABLE "risk_value_mapping" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "risk_matrix_id" uuid NOT NULL,
        "value_key" varchar(100) NOT NULL,
        "value_label" varchar(255) NOT NULL,
        "sort_order" integer,
        "color" varchar(7),
        "description" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted" boolean NOT NULL DEFAULT false,
        CONSTRAINT "PK_risk_value_mapping" PRIMARY KEY ("id")
      )
    `);

    // Create unique index for risk_value_mapping
    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_risk_value_mapping_matrix" 
      ON "risk_value_mapping"("risk_matrix_id", "value_key") 
      WHERE deleted = false
    `);

    // Create foreign key constraint for risk_value_mapping
    await queryRunner.query(`
      ALTER TABLE "risk_value_mapping" 
      ADD CONSTRAINT "FK_risk_value_mapping_matrix" 
      FOREIGN KEY ("risk_matrix_id") REFERENCES "risk_matrix_master"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Create risk_level_mapping table
    await queryRunner.query(`
      CREATE TABLE "risk_level_mapping" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "risk_matrix_id" uuid NOT NULL,
        "risk_value_mapping_ids" text,
        "level_label" varchar(255) NOT NULL,
        "min_value" integer,
        "max_value" integer,
        "color" varchar(7),
        "sort_order" integer NOT NULL DEFAULT 0,
        "description" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted" boolean NOT NULL DEFAULT false,
        CONSTRAINT "PK_risk_level_mapping" PRIMARY KEY ("id")
      )
    `);

    // Create unique index for risk_level_mapping (changed to use level_label instead of risk_level)
    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_risk_level_mapping_matrix"
      ON "risk_level_mapping"("risk_matrix_id", "level_label")
      WHERE deleted = false
    `);

    // Create foreign key constraint for risk_level_mapping
    await queryRunner.query(`
      ALTER TABLE "risk_level_mapping"
      ADD CONSTRAINT "FK_risk_level_mapping_matrix"
      FOREIGN KEY ("risk_matrix_id") REFERENCES "risk_matrix_master"("id")
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Create junction table for many-to-many relationship between risk_level_mapping and risk_value_mapping
    await queryRunner.query(`
      CREATE TABLE "risk_level_mapping_value_mappings" (
        "risk_level_mapping_id" uuid NOT NULL,
        "risk_value_mapping_id" uuid NOT NULL,
        CONSTRAINT "PK_risk_level_mapping_value_mappings" PRIMARY KEY ("risk_level_mapping_id", "risk_value_mapping_id")
      )
    `);

    // Create foreign key constraints for junction table
    await queryRunner.query(`
      ALTER TABLE "risk_level_mapping_value_mappings"
      ADD CONSTRAINT "FK_risk_level_mapping_value_mappings_level"
      FOREIGN KEY ("risk_level_mapping_id") REFERENCES "risk_level_mapping"("id")
      ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "risk_level_mapping_value_mappings"
      ADD CONSTRAINT "FK_risk_level_mapping_value_mappings_value"
      FOREIGN KEY ("risk_value_mapping_id") REFERENCES "risk_value_mapping"("id")
      ON DELETE CASCADE ON UPDATE CASCADE
    `);

    // Create indexes for junction table
    await queryRunner.query(`
      CREATE INDEX "IDX_risk_level_mapping_value_mappings_level"
      ON "risk_level_mapping_value_mappings" ("risk_level_mapping_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_risk_level_mapping_value_mappings_value"
      ON "risk_level_mapping_value_mappings" ("risk_value_mapping_id")
    `);

    // Note: risk_value_mapping_ids is stored as simple-array (comma-separated text) for backup
    // The main relationship is handled through the junction table
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop junction table foreign key constraints and indexes
    await queryRunner.query(`DROP INDEX "IDX_risk_level_mapping_value_mappings_value"`);
    await queryRunner.query(`DROP INDEX "IDX_risk_level_mapping_value_mappings_level"`);
    await queryRunner.query(`ALTER TABLE "risk_level_mapping_value_mappings" DROP CONSTRAINT "FK_risk_level_mapping_value_mappings_value"`);
    await queryRunner.query(`ALTER TABLE "risk_level_mapping_value_mappings" DROP CONSTRAINT "FK_risk_level_mapping_value_mappings_level"`);

    // Drop junction table
    await queryRunner.query(`DROP TABLE "risk_level_mapping_value_mappings"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "risk_level_mapping" DROP CONSTRAINT "FK_risk_level_mapping_matrix"`);
    await queryRunner.query(`ALTER TABLE "risk_value_mapping" DROP CONSTRAINT "FK_risk_value_mapping_matrix"`);
    await queryRunner.query(`ALTER TABLE "risk_matrix_cell" DROP CONSTRAINT "FK_risk_matrix_cell_value_mapping"`);
    await queryRunner.query(`ALTER TABLE "risk_matrix_cell" DROP CONSTRAINT "FK_risk_matrix_cell_matrix"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "idx_risk_level_mapping_matrix"`);
    await queryRunner.query(`DROP INDEX "idx_risk_value_mapping_matrix"`);
    await queryRunner.query(`DROP INDEX "idx_risk_matrix_cell_value_mapping"`);
    await queryRunner.query(`DROP INDEX "idx_risk_matrix_master_code_companyId"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "risk_level_mapping"`);
    await queryRunner.query(`DROP TABLE "risk_value_mapping"`);
    await queryRunner.query(`DROP TABLE "risk_matrix_cell"`);

    // Remove new columns from risk_matrix_master
    await queryRunner.query(`ALTER TABLE "risk_matrix_master" DROP COLUMN "matrix_name"`);
    await queryRunner.query(`ALTER TABLE "risk_matrix_master" DROP COLUMN "matrix_code"`);
  }
}
