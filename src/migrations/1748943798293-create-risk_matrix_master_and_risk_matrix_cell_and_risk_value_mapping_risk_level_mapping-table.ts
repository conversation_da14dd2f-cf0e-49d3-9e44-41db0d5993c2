import {MigrationInterface, QueryRunner} from "typeorm";

export class createRiskMatrixMasterAndRiskMatrixCellAndRiskValueMappingRiskLevelMappingTable1748943798293 implements MigrationInterface {
    name = 'createRiskMatrixMasterAndRiskMatrixCellAndRiskValueMappingRiskLevelMappingTable1748943798293'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "risk_value_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "riskMatrixId" uuid NOT NULL, "valueKey" character varying(100) NOT NULL, "valueId" uuid NOT NULL, CONSTRAINT "PK_dff1a945a2abfd0fa8eed9261a4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "idx_risk_value_mapping_matrix" ON "risk_value_mapping" ("riskMatrixId", "valueKey") WHERE deleted = false`);
        await queryRunner.query(`CREATE TABLE "risk_matrix_cell" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "riskMatrixId" uuid NOT NULL, "riskValueMappingId" uuid, "cellPositions" text, CONSTRAINT "PK_74b147c32b36ff3cd35127ae372" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "idx_risk_matrix_cell_value_mapping" ON "risk_matrix_cell" ("riskMatrixId", "riskValueMappingId") WHERE deleted = false`);
        await queryRunner.query(`CREATE TYPE "risk_matrix_master_status_enum" AS ENUM('active', 'inactive')`);
        await queryRunner.query(`CREATE TABLE "risk_matrix_master" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "matrixCode" character varying(50) NOT NULL, "matrixName" character varying(255), "rows" integer NOT NULL, "columns" integer NOT NULL, "status" "risk_matrix_master_status_enum" NOT NULL DEFAULT 'active', "rowsName" text, "columnsName" text, "companyId" uuid NOT NULL, "createdUserId" uuid, "updatedUserId" uuid, CONSTRAINT "PK_cbcb933d57fe6c94e8505da1c4b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "idx_risk_matrix_master_code_companyId" ON "risk_matrix_master" ("matrixCode", "companyId") WHERE deleted = false`);
        await queryRunner.query(`CREATE INDEX "idx_risk_matrix_master_companyId" ON "risk_matrix_master" ("companyId") WHERE deleted = false`);
        await queryRunner.query(`CREATE TABLE "risk_level_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "deleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "riskMatrixId" uuid NOT NULL, "riskValueMappingIds" uuid array NOT NULL DEFAULT '{}', "priorityMasterId" uuid, CONSTRAINT "PK_60334702b55ff906c6d308340e2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "idx_risk_level_mapping_matrix" ON "risk_level_mapping" ("riskMatrixId") WHERE deleted = false`);
        await queryRunner.query(`ALTER TABLE "risk_value_mapping" ADD CONSTRAINT "FK_7d79447f1628485bd25b9712daf" FOREIGN KEY ("riskMatrixId") REFERENCES "risk_matrix_master"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "risk_matrix_cell" ADD CONSTRAINT "FK_4d858875cac4e59592dc985f9ec" FOREIGN KEY ("riskMatrixId") REFERENCES "risk_matrix_master"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "risk_matrix_cell" ADD CONSTRAINT "FK_c2a01adf8141da7004c88b2e897" FOREIGN KEY ("riskValueMappingId") REFERENCES "risk_value_mapping"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "risk_matrix_master" ADD CONSTRAINT "FK_d365f735bf75996cd9504442748" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "risk_matrix_master" ADD CONSTRAINT "FK_e7d8d0e33b9c9f3c2750b59fd6a" FOREIGN KEY ("createdUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "risk_matrix_master" ADD CONSTRAINT "FK_dd222a24c2f314181a5d0c3a0c0" FOREIGN KEY ("updatedUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "risk_level_mapping" ADD CONSTRAINT "FK_4808f2b17e9bcf221681e44949a" FOREIGN KEY ("riskMatrixId") REFERENCES "risk_matrix_master"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "risk_level_mapping" ADD CONSTRAINT "FK_40c459c8441a58ea723d443f640" FOREIGN KEY ("priorityMasterId") REFERENCES "priority_master"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        }

    public async down(queryRunner: QueryRunner): Promise<void> {
        }

}
