import { EntityRepository, Repository, SelectQueryBuilder, Not, getConnection, Connection } from 'typeorm';
import { InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { TokenPayloadModel, CommonStatus } from 'svm-nest-lib-v3';

import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { RiskMatrixCell } from './risk-matrix-cell.entity';
import { RiskValueMapping } from './risk-value-mapping.entity';
import { RiskLevelMapping } from './risk-level-mapping.entity';
import { CreateRiskMatrixDTO, UpdateRiskMatrixDTO, ListRiskMatrixQueryDTO } from './dto';
import { ConfigureMatrixCellsDTO, ConfigureValueMappingsDTO, ConfigureLevelMappingsDTO } from './dto/matrix-configuration.dto';
import { StatusCommon } from '../../commons/enums';

@EntityRepository(RiskMatrixMaster)
export class RiskMatrixMasterRepository extends Repository<RiskMatrixMaster> {
  constructor(
      private readonly connection: Connection,
    ) {
      super();
    }
  async createRiskMatrix(
    createRiskMatrixDto: CreateRiskMatrixDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixMaster> {
    // const queryRunner = getConnection().createQueryRunner();
    // await queryRunner.connect();
    // await queryRunner.startTransaction();

    try {
      // Check if there's already an active risk matrix for this company
      const existingActiveMatrix = await this.findOne({
        where: {
          companyId: token.companyId,
          status: StatusCommon.ACTIVE,
          deleted: false,
        },
      });

      if (existingActiveMatrix && createRiskMatrixDto.status === StatusCommon.ACTIVE) {
        throw new BadRequestException('Only one risk matrix can be active at a time');
      }

      // Check if matrix code already exists for this company
      const existingMatrixCode = await this.findOne({
        where: {
          matrixCode: createRiskMatrixDto.matrixCode,
          companyId: token.companyId,
          deleted: false,
        },
      });

      if (existingMatrixCode) {
        throw new BadRequestException('Matrix code already exists for this company');
      }

      // Create the main risk matrix
      const riskMatrix = this.create({
        matrixCode: createRiskMatrixDto.matrixCode,
        // matrixName: createRiskMatrixDto.matrixName,
        rows: createRiskMatrixDto.rows,
        columns: createRiskMatrixDto.columns,
        rowsName: createRiskMatrixDto.rowsName,
        columnsName: createRiskMatrixDto.columnsName,
        status: createRiskMatrixDto.status || StatusCommon.ACTIVE,
        companyId: token.companyId,
        createdUserId: token.id,
      });

      const savedMatrix = await this.save(riskMatrix);
// Create value mappings if provided
if (createRiskMatrixDto.valueMappings && createRiskMatrixDto.valueMappings.length > 0) {
  const valueMappingRepository = this.manager.getRepository(RiskValueMapping);
  const valueMappings = createRiskMatrixDto.valueMappings.map(mappingDto =>
    valueMappingRepository.create({
      ...mappingDto,
      id: mappingDto.valueId,
      riskMatrixId: savedMatrix.id,
    })
  );
  await valueMappingRepository.save(valueMappings);
}
      // Create matrix cells if provided
      if (createRiskMatrixDto.cells && createRiskMatrixDto.cells.length > 0) {
        const cellRepository = this.manager.getRepository(RiskMatrixCell);
        const cells = createRiskMatrixDto.cells.map(cellDto =>
          cellRepository.create({
            ...cellDto,
            riskMatrixId: savedMatrix.id,
          })
        );
        await cellRepository.save(cells);
      }

      

      // Create level mappings if provided
      if (createRiskMatrixDto.levelMappings && createRiskMatrixDto.levelMappings.length > 0) {
        const levelMappingRepository = this.manager.getRepository(RiskLevelMapping);
        const levelMappings = createRiskMatrixDto.levelMappings.map(mappingDto =>
          levelMappingRepository.create({
            ...mappingDto,
            riskMatrixId: savedMatrix.id,
          })
        );
        await this.save(levelMappings);
      }

      return savedMatrix;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create risk matrix');
    }
  }

  async listRiskMatrix(
    query: ListRiskMatrixQueryDTO,
    token: TokenPayloadModel,
  ): Promise<{ data: RiskMatrixMaster[]; total: number }> {
    try {
      const {
        page = 1,
        pageSize = 20,
        content,
        status,
        sort,
        createdAtFrom,
        createdAtTo,
      } = query;

      const queryBuilder = this.buildListQuery(token.companyId);

      // Search by content (searching in rows/columns)
      if (content) {
        queryBuilder.andWhere('(rm.rows::text ILIKE :content OR rm.columns::text ILIKE :content)', {
          content: `%${content}%`,
        });
      }

      // Filter by status
      if (status) {
        queryBuilder.andWhere('rm.status = :status', { status });
      }

      // Date range filter
      if (createdAtFrom) {
        queryBuilder.andWhere('rm.createdAt >= :createdAtFrom', { createdAtFrom });
      }
      if (createdAtTo) {
        queryBuilder.andWhere('rm.createdAt <= :createdAtTo', { createdAtTo });
      }

      // Sorting
      if (sort) {
        this.applySorting(queryBuilder, sort);
      } else {
        queryBuilder.orderBy('rm.createdAt', 'DESC');
      }

      // Pagination
      if (pageSize !== -1) {
        queryBuilder.skip((page - 1) * pageSize).take(pageSize);
      }

      const [data, total] = await queryBuilder.getManyAndCount();

      return { data, total };
    } catch (error) {
      throw new InternalServerErrorException('Failed to list risk matrices');
    }
  }

  async getDetailRiskMatrixById(id: string): Promise<RiskMatrixMaster> {
    try {
      const riskMatrix = await this.createQueryBuilder('rm')
        .leftJoinAndSelect('rm.createdUser', 'createdUser')
        .leftJoinAndSelect('rm.updatedUser', 'updatedUser')
        .leftJoinAndSelect('rm.cells', 'cells')
        .leftJoinAndSelect('rm.valueMappings', 'valueMappings')
        .leftJoinAndSelect('rm.levelMappings', 'levelMappings')
        .where('rm.id = :id', { id })
        .andWhere('rm.deleted = false')
        .getOne();

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      return riskMatrix;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get risk matrix details');
    }
  }

  async getMatrixConfiguration(id: string, token: TokenPayloadModel) {
    try {
      const matrix = await this.findOne({
        where: { id, companyId: token.companyId, deleted: false },
        relations: ['cells', 'valueMappings', 'levelMappings'],
      });

      if (!matrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      return {
        matrix,
        cells: matrix.cells || [],
        valueMappings: matrix.valueMappings || [],
        levelMappings: matrix.levelMappings || [],
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get matrix configuration');
    }
  }

  async updateRiskMatrix(
    id: string,
    updateRiskMatrixDto: UpdateRiskMatrixDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixMaster> {
    try {
      const riskMatrix = await this.findOne({
        where: { id, companyId: token.companyId, deleted: false },
      });

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      // If updating to active status, check if there's already an active matrix
      if (
        updateRiskMatrixDto.status === StatusCommon.ACTIVE &&
        riskMatrix.status !== StatusCommon.ACTIVE
      ) {
        const existingActiveMatrix = await this.findOne({
          where: {
            companyId: token.companyId,
            status: StatusCommon.ACTIVE,
            deleted: false,
            id: Not(id),
          },
        });

        if (existingActiveMatrix) {
          throw new BadRequestException('Only one risk matrix can be active at a time');
        }
      }

      Object.assign(riskMatrix, {
        ...updateRiskMatrixDto,
        updatedUserId: token.id,
        updatedAt: new Date(),
      });

      return await this.save(riskMatrix);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update risk matrix');
    }
  }

  async deleteRiskMatrix(id: string, companyId: string): Promise<void> {
    try {
      const riskMatrix = await this.findOne({
        where: { id, companyId, deleted: false },
      });

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      riskMatrix.deleted = true;

      await this.save(riskMatrix);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete risk matrix');
    }
  }

  private buildListQuery(companyId: string): SelectQueryBuilder<RiskMatrixMaster> {
    return this.createQueryBuilder('rm')
      .leftJoinAndSelect('rm.createdUser', 'createdUser')
      .leftJoinAndSelect('rm.updatedUser', 'updatedUser')
      .where('rm.companyId = :companyId', { companyId })
      .andWhere('rm.deleted = false');
  }

  async configureMatrixCells(
    configureDto: ConfigureMatrixCellsDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixCell[]> {
    const queryRunner = getConnection().createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify matrix exists and belongs to company
      const matrix = await this.findOne({
        where: { id: configureDto.riskMatrixId, companyId: token.companyId, deleted: false },
      });

      if (!matrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      const cellRepository = queryRunner.manager.getRepository(RiskMatrixCell);

      // Delete existing cells for this matrix
      await cellRepository.delete({ riskMatrixId: configureDto.riskMatrixId });

      // Create new cells
      const cells = configureDto.cells.map(cellDto =>
        cellRepository.create({
          ...cellDto,
          riskMatrixId: configureDto.riskMatrixId,
        })
      );

      const savedCells = await queryRunner.manager.save(cells);
      await queryRunner.commitTransaction();
      return savedCells;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to configure matrix cells');
    } finally {
      await queryRunner.release();
    }
  }

  async configureValueMappings(
    configureDto: ConfigureValueMappingsDTO,
    token: TokenPayloadModel,
  ): Promise<RiskValueMapping[]> {
    const queryRunner = getConnection().createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify matrix exists and belongs to company
      const matrix = await this.findOne({
        where: { id: configureDto.riskMatrixId, companyId: token.companyId, deleted: false },
      });

      if (!matrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      const valueMappingRepository = queryRunner.manager.getRepository(RiskValueMapping);

      // Delete existing value mappings for this matrix
      await valueMappingRepository.delete({ riskMatrixId: configureDto.riskMatrixId });

      // Create new value mappings
      const valueMappings = configureDto.valueMappings.map(mappingDto =>
        valueMappingRepository.create({
          ...mappingDto,
          riskMatrixId: configureDto.riskMatrixId,
        })
      );

      const savedMappings = await queryRunner.manager.save(valueMappings);
      await queryRunner.commitTransaction();
      return savedMappings;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to configure value mappings');
    } finally {
      await queryRunner.release();
    }
  }

  async configureLevelMappings(
    configureDto: ConfigureLevelMappingsDTO,
    token: TokenPayloadModel,
  ): Promise<RiskLevelMapping[]> {
    const queryRunner = getConnection().createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify matrix exists and belongs to company
      const matrix = await this.findOne({
        where: { id: configureDto.riskMatrixId, companyId: token.companyId, deleted: false },
      });

      if (!matrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      const levelMappingRepository = queryRunner.manager.getRepository(RiskLevelMapping);

      // Delete existing level mappings for this matrix
      await levelMappingRepository.delete({ riskMatrixId: configureDto.riskMatrixId });

      // Create new level mappings
      const levelMappings = configureDto.levelMappings.map(mappingDto =>
        levelMappingRepository.create({
          ...mappingDto,
          riskMatrixId: configureDto.riskMatrixId,
        })
      );

      const savedMappings = await queryRunner.manager.save(levelMappings);
      await queryRunner.commitTransaction();
      return savedMappings;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to configure level mappings');
    } finally {
      await queryRunner.release();
    }
  }

  private applySorting(queryBuilder: SelectQueryBuilder<RiskMatrixMaster>, sort: string): void {
    const sortFields = sort.split(';');
    sortFields.forEach((field) => {
      const [fieldName, direction] = field.split(':');
      const sortDirection = direction === '1' ? 'ASC' : 'DESC';

      switch (fieldName) {
        case 'rows':
          queryBuilder.addOrderBy('rm.rows', sortDirection);
          break;
        case 'columns':
          queryBuilder.addOrderBy('rm.columns', sortDirection);
          break;
        case 'status':
          queryBuilder.addOrderBy('rm.status', sortDirection);
          break;
        case 'createdAt':
          queryBuilder.addOrderBy('rm.createdAt', sortDirection);
          break;
        default:
          break;
      }
    });
  }
}