import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { DBIndexes } from '../../commons/consts/db.const';

@Entity()
@Index(DBIndexes.IDX_RISK_VALUE_MAPPING_MATRIX, ['riskMatrixId', 'valueKey'], {
  unique: true,
  where: 'deleted = false',
})
export class RiskValueMapping extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public riskMatrixId: string;

  @Column({ type: 'varchar', length: 100 })
  public valueKey: string; // e.g., 'text', 'test2'
  
  @Column({ type: 'uuid' })
  public valueId: string;
  // @Column({ type: 'varchar', length: 255 })
  // public valueLabel: string; // Display label for the value

  // @Column({ type: 'int', nullable: true })
  // public sortOrder: number;

  // @Column({ type: 'varchar', length: 7, nullable: true })
  // public color: string; // Hex color code

  // @Column({ type: 'text', nullable: true })
  // public description: string;

  // Relationships
  @ManyToOne(() => RiskMatrixMaster, (matrix) => matrix.valueMappings, { onDelete: 'CASCADE' })
  riskMatrix: RiskMatrixMaster;
}
