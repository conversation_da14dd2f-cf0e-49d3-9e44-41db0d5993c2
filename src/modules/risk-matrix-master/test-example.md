# Risk Matrix Master - Test Examples

## Example 1: Create a Complete Risk Matrix

```bash
POST /risk-matrix-master
Content-Type: application/json
Authorization: Bearer <token>

{
  "matrixCode": "RM001",
  "matrixName": "Standard 4x4 Risk Matrix",
  "rows": 4,
  "columns": 4,
  "status": "active",
  "cells": [
    {
      "riskValueMappingId": "uuid-of-text-mapping",
      "cellPositions": ["r1c1", "r1c2"],
      "riskLevel": "low",
      "backgroundColor": "#00FF00",
      "description": "Low risk cells for text value"
    },
    {
      "riskValueMappingId": "uuid-of-test2-mapping",
      "cellPositions": ["r4c4"],
      "riskLevel": "high",
      "backgroundColor": "#FF0000",
      "description": "High risk cell for test2 value"
    }
  ],
  "valueMappings": [
    {
      "valueKey": "text",
      "valueLabel": "Text Value",
      "sortOrder": 1,
      "color": "#0000FF",
      "description": "Standard text mapping"
    },
    {
      "valueKey": "test2",
      "valueLabel": "Test Value 2",
      "sortOrder": 2,
      "color": "#FF00FF"
    }
  ],
  "levelMappings": [
    {
      "riskLevels": ["negligible"],
      "levelLabel": "Negligible Risk",
      "minValue": 0,
      "maxValue": 1,
      "color": "#90EE90",
      "sortOrder": 1,
      "description": "Very low risk level"
    },
    {
      "riskLevels": ["low"],
      "levelLabel": "Low Risk",
      "minValue": 2,
      "maxValue": 4,
      "color": "#00FF00",
      "sortOrder": 2
    },
    {
      "riskLevels": ["medium"],
      "levelLabel": "Medium Risk",
      "minValue": 5,
      "maxValue": 8,
      "color": "#FFFF00",
      "sortOrder": 3
    },
    {
      "riskLevels": ["high"],
      "levelLabel": "High Risk",
      "minValue": 9,
      "maxValue": 16,
      "color": "#FF0000",
      "sortOrder": 4
    },
    {
      "riskLevels": ["low", "medium"],
      "levelLabel": "Low to Medium Risk",
      "minValue": 2,
      "maxValue": 8,
      "color": "#ADFF2F",
      "sortOrder": 5,
      "description": "Combined low to medium risk level"
    }
  ]
}
```

## Example 2: Get Matrix Configuration

```bash
GET /risk-matrix-master/{matrix-id}/configuration
Authorization: Bearer <token>
```

## Example 3: Configure Matrix Cells Separately

```bash
POST /risk-matrix-master/configure-cells
Content-Type: application/json
Authorization: Bearer <token>

{
  "riskMatrixId": "uuid-of-matrix",
  "cells": [
    {
      "riskValueMappingId": "uuid-of-select-mapping",
      "cellPositions": ["r1c1"],
      "riskLevel": "negligible",
      "backgroundColor": "#90EE90"
    },
    {
      "riskValueMappingId": "uuid-of-select-mapping",
      "cellPositions": ["r1c2", "r2c1"],
      "riskLevel": "low",
      "backgroundColor": "#00FF00"
    },
    {
      "riskValueMappingId": "uuid-of-select-mapping",
      "cellPositions": ["r2c2"],
      "riskLevel": "medium",
      "backgroundColor": "#FFFF00"
    }
  ]
}
```

## Example 4: Configure Value Mappings

```bash
POST /risk-matrix-master/configure-value-mappings
Content-Type: application/json
Authorization: Bearer <token>

{
  "riskMatrixId": "uuid-of-matrix",
  "valueMappings": [
    {
      "valueKey": "text",
      "valueLabel": "Text Option",
      "sortOrder": 1,
      "color": "#0000FF"
    },
    {
      "valueKey": "test2",
      "valueLabel": "Test Option 2",
      "sortOrder": 2,
      "color": "#800080"
    }
  ]
}
```

## Example 5: Configure Level Mappings

```bash
POST /risk-matrix-master/configure-level-mappings
Content-Type: application/json
Authorization: Bearer <token>

{
  "riskMatrixId": "uuid-of-matrix",
  "levelMappings": [
    {
      "riskLevels": ["negligible"],
      "levelLabel": "Negligible",
      "color": "#90EE90",
      "sortOrder": 1
    },
    {
      "riskLevels": ["low"],
      "levelLabel": "Low",
      "color": "#00FF00",
      "sortOrder": 2
    },
    {
      "riskLevels": ["medium"],
      "levelLabel": "Medium",
      "color": "#FFFF00",
      "sortOrder": 3
    },
    {
      "riskLevels": ["high"],
      "levelLabel": "High",
      "color": "#FF0000",
      "sortOrder": 4
    },
    {
      "riskLevels": ["negligible", "low"],
      "levelLabel": "Negligible to Low",
      "color": "#98FB98",
      "sortOrder": 5
    },
    {
      "riskLevels": ["medium", "high"],
      "levelLabel": "Medium to High",
      "color": "#FFA500",
      "sortOrder": 6
    }
  ]
}
```

## Testing Workflow

1. **Create Matrix**: Use Example 1 to create a complete risk matrix
2. **Verify Creation**: Use GET /risk-matrix-master to list matrices
3. **Get Configuration**: Use Example 2 to get full configuration
4. **Update Components**: Use Examples 3-5 to update individual components
5. **Test Validation**: Try creating duplicate matrix codes (should fail)
6. **Test Business Rules**: Try creating multiple active matrices (should fail)
