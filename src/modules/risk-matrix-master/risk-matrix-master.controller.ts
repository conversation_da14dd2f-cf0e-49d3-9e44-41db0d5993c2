import {
  Param,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Controller,
  Query,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { I18n, I18nContext } from 'nestjs-i18n';
import {
  Roles,
  RoleScope,
  AuthGuard,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
  RequiredPermissions,
} from 'svm-nest-lib-v3';
import {
  ApiParam,
  ApiBody,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';

import { CreateRiskMatrixDTO, UpdateRiskMatrixDTO, ListRiskMatrixQueryDTO } from './dto';
import { ConfigureMatrixCellsDTO, ConfigureValueMappingsDTO, ConfigureLevelMappingsDTO } from './dto/matrix-configuration.dto';
import { RiskMatrixMasterService } from './risk-matrix-master.service';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';

@ApiTags('Risk Matrix Master')
@Controller()
export class RiskMatrixMasterController {
  constructor(private readonly riskMatrixMasterService: RiskMatrixMasterService) {}

  @ApiResponse({ description: 'Create risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Create risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Create risk matrix', operationId: 'createRiskMatrix' })
  @ApiBearerAuth()
  @ApiBody({ type: CreateRiskMatrixDTO })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
  //   action: ActionEnum.CREATE,
  // })
  @Post('/risk-matrix-master')
  async createRiskMatrix(
    @Body() body: CreateRiskMatrixDTO,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    console.log("wwwwww");
    await this.riskMatrixMasterService.createRiskMatrix(body, token);
   
    
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'List risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List risk matrix', operationId: 'listRiskMatrix' })
  @ApiBearerAuth()
  @ApiQuery({
    description: 'Paginate params',
    type: ListRiskMatrixQueryDTO,
    required: false,
  })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
    action: ActionEnum.VIEW,
  })
  @Get('/risk-matrix-master')
  async listRiskMatrix(@Query() query: ListRiskMatrixQueryDTO, @TokenDecorator() token: TokenPayloadModel) {
    return this.riskMatrixMasterService.listRiskMatrix(query, token);
  }

  @ApiResponse({ description: 'Detail risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Detail risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Detail risk matrix', operationId: 'detailRiskMatrix' })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Risk matrix id',
  })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
    action: ActionEnum.VIEW,
  })
  @Get('/risk-matrix-master/:id')
  async detailRiskMatrix(@Param('id', ParseUUIDPipe) id: string) {
    return this.riskMatrixMasterService.getDetailRiskMatrix(id);
  }

  @ApiResponse({ description: 'Update risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Update risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Update risk matrix', operationId: 'updateRiskMatrix' })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Risk matrix id',
  })
  @ApiBody({ type: UpdateRiskMatrixDTO })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
    action: ActionEnum.UPDATE,
  })
  @Put('/risk-matrix-master/:id')
  async updateRiskMatrixById(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: UpdateRiskMatrixDTO,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    await this.riskMatrixMasterService.updateRiskMatrix(id, body, token);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Delete risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Delete risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Delete risk matrix', operationId: 'deleteRiskMatrix' })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Risk matrix id',
  })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
    action: ActionEnum.DELETE,
  })
  @Delete('/risk-matrix-master/:id')
  async deleteRiskMatrix(
    @Param('id', ParseUUIDPipe) id: string,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    await this.riskMatrixMasterService.deleteRiskMatrix(id, token.companyId);
    return {
      message: await i18n.t('common.DELETE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Get matrix configuration success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Get matrix configuration error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Get matrix configuration', operationId: 'getMatrixConfiguration' })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Risk matrix id',
  })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
    action: ActionEnum.VIEW,
  })
  @Get('/risk-matrix-master/:id/configuration')
  async getMatrixConfiguration(
    @Param('id', ParseUUIDPipe) id: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.riskMatrixMasterService.getMatrixConfiguration(id, token);
  }

  @ApiResponse({ description: 'Configure matrix cells success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Configure matrix cells error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Configure matrix cells', operationId: 'configureMatrixCells' })
  @ApiBearerAuth()
  @ApiBody({ type: ConfigureMatrixCellsDTO })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
    action: ActionEnum.UPDATE,
  })
  @Post('/risk-matrix-master/configure-cells')
  async configureMatrixCells(
    @Body() body: ConfigureMatrixCellsDTO,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    await this.riskMatrixMasterService.configureMatrixCells(body, token);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Configure value mappings success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Configure value mappings error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Configure value mappings', operationId: 'configureValueMappings' })
  @ApiBearerAuth()
  @ApiBody({ type: ConfigureValueMappingsDTO })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
    action: ActionEnum.UPDATE,
  })
  @Post('/risk-matrix-master/configure-value-mappings')
  async configureValueMappings(
    @Body() body: ConfigureValueMappingsDTO,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    await this.riskMatrixMasterService.configureValueMappings(body, token);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Configure level mappings success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Configure level mappings error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Configure level mappings', operationId: 'configureLevelMappings' })
  @ApiBearerAuth()
  @ApiBody({ type: ConfigureLevelMappingsDTO })
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_COMMON + '::' + 'Risk Matrix Master',
    action: ActionEnum.UPDATE,
  })
  @Post('/risk-matrix-master/configure-level-mappings')
  async configureLevelMappings(
    @Body() body: ConfigureLevelMappingsDTO,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    await this.riskMatrixMasterService.configureLevelMappings(body, token);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }
}