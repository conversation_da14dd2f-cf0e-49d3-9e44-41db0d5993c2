import { <PERSON>tity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { DBIndexes } from '../../../commons/consts/db.const';

@Entity()
@Index(DBIndexes.IDX_RISK_VALUE_MAPPING_MATRIX, ['riskMatrixId', 'value'], {
  unique: true,
  where: 'deleted = false',
})
export class RiskValueMapping extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public riskMatrixId: string;

  @Column({ type: 'varchar', length: 100 })
  public riskValue: string; // e.g., 'text', 'test2'

  @Column({ type: 'varchar', length: 7, nullable: true })
  public color: string; // Hex color code

  // Relationships
  @ManyToOne(() => RiskMatrixMaster, (matrix) => matrix.valueMappings, { onDelete: 'CASCADE' })
  riskMatrix: RiskMatrixMaster;
}
