import { <PERSON>tity, Column, PrimaryGeneratedColumn, ManyToOne, ManyToMany, JoinTable, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { RiskValueMapping } from './risk-value-mapping.entity';
import { DBIndexes } from '../../../commons/consts/db.const';
import { PriorityMaster } from '../../priority-master/priority-master.entity';

export enum RiskLevelEnum {
  NEGLIGIBLE = 'negligible',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

@Entity()
@Index(DBIndexes.IDX_RISK_LEVEL_MAPPING_MATRIX, ['riskMatrixId'], {
  unique: true,
  where: 'deleted = false',
})
export class RiskLevelMapping extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public riskMatrixId: string;

  @Column({
    type: 'uuid',
    array: true,
    default: [],
  })
  public riskValueMappingIds: string[]; // Array of RiskValueMapping UUIDs

  @Column({ type: 'uuid', nullable: true })
  public priorityMasterId: string;

  // Relationships
  @ManyToOne(() => RiskMatrixMaster, (matrix) => matrix.levelMappings, { onDelete: 'CASCADE' })
  riskMatrix: RiskMatrixMaster;

  @ManyToOne(() => PriorityMaster, (priorityMaster) => priorityMaster.riskLevelMapping, { onDelete: 'CASCADE' })
  priorityMaster: PriorityMaster;

}
