import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { RiskValueMapping } from './risk-value-mapping.entity';
import { DBIndexes } from '../../commons/consts/db.const';

@Entity()
@Index(DBIndexes.IDX_RISK_MATRIX_CELL_VALUE_MAPPING, ['riskMatrixId', 'riskValueMappingId'], {
  where: 'deleted = false',
})
export class RiskMatrixCell extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public riskMatrixId: string;

  @Column({ type: 'uuid', nullable: true })
  public riskValueMappingId: string; // Reference to RiskValueMapping

  @Column({ type: 'simple-array', nullable: true })
  public cellPositions: string[]; // Array of positions like ['r1c1', 'r2c2', 'r3c3']

  // @Column({ type: 'simple-array', nullable: true })
  // public rowNames: string[]; // Array of row names like ['Probability', 'Likelihood']

  // @Column({ type: 'simple-array', nullable: true })
  // public columnNames: string[]; // Array of column names like ['Impact', 'Severity']

  // @Column({ type: 'varchar', length: 50, nullable: true })
  // public riskLevel: string; // 'negligible', 'low', 'medium', 'high'

  // @Column({ type: 'varchar', length: 7, nullable: true })
  // public backgroundColor: string; // Hex color code

  // @Column({ type: 'text', nullable: true })
  // public description: string;

  // Relationships
  @ManyToOne(() => RiskMatrixMaster, (matrix) => matrix.cells, { onDelete: 'CASCADE' })
  riskMatrix: RiskMatrixMaster;

  @ManyToOne(() => RiskValueMapping, { onDelete: 'SET NULL' })
  riskValueMapping: RiskValueMapping;
}
