import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { DBIndexes } from '../../commons/consts/db.const';

@Entity()
@Index(DBIndexes.IDX_RISK_MATRIX_CELL_POSITION, ['riskMatrixId', 'rowIndex', 'columnIndex'], {
  unique: true,
  where: 'deleted = false',
})
export class RiskMatrixCell extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public riskMatrixId: string;

  @Column({ type: 'int' })
  public rowIndex: number;

  @Column({ type: 'int' })
  public columnIndex: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  public cellValue: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  public riskLevel: string; // 'negligible', 'low', 'medium', 'high'

  @Column({ type: 'varchar', length: 7, nullable: true })
  public backgroundColor: string; // Hex color code

  // @Column({ type: 'text', nullable: true })
  // public description: string;

  // Relationships
  @ManyToOne(() => RiskMatrixMaster, (matrix) => matrix.cells, { onDelete: 'CASCADE' })
  riskMatrix: RiskMatrixMaster;
}
