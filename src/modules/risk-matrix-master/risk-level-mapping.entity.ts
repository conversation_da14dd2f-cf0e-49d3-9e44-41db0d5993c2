import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { DBIndexes } from '../../commons/consts/db.const';

export enum RiskLevelEnum {
  NEGLIGIBLE = 'negligible',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

@Entity()
@Index(DBIndexes.IDX_RISK_LEVEL_MAPPING_MATRIX, ['riskMatrixId', 'levelLabel'], {
  unique: true,
  where: 'deleted = false',
})
export class RiskLevelMapping extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public riskMatrixId: string;

  @Column({ type: 'simple-array', nullable: true })
  public riskLevels: string[]; // Array of risk levels for multiple selection

  @Column({ type: 'varchar', length: 255 })
  public levelLabel: string; // Display label for the level combination

  @Column({ type: 'int', nullable: true })
  public minValue: number;

  @Column({ type: 'int', nullable: true })
  public maxValue: number;

  @Column({ type: 'varchar', length: 7, nullable: true })
  public color: string; // Hex color code

  @Column({ type: 'int', default: 0 })
  public sortOrder: number;

  @Column({ type: 'text', nullable: true })
  public description: string;

  // Relationships
  @ManyToOne(() => RiskMatrixMaster, (matrix) => matrix.levelMappings, { onDelete: 'CASCADE' })
  riskMatrix: RiskMatrixMaster;
}
