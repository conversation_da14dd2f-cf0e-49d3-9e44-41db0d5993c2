# Risk Matrix Master Module

## Overview
The Risk Matrix Master module allows users to define and manage risk matrices as defined by the company. This module provides a complete CRUD interface for managing risk matrix configurations.

## Features

### 1. Create New Risk Matrix
- Users can define the number of rows and columns for the matrix
- Both fields accept only positive integers and are mandatory
- Only one risk matrix can be active at a time per company
- Save button is enabled only when both rows and columns fields are populated

### 2. List Risk Matrices
- Displays a list/grid of previously created risk matrix records
- Shows Matrix ID, Rows, Columns, Status (Active/Inactive), Created Date, Created By
- Supports filtering by status (Active/Inactive)
- Supports pagination and sorting
- Search functionality for rows/columns

### 3. View Risk Matrix Details
- View detailed information about a specific risk matrix
- Shows creator and updater information
- Displays creation and update timestamps

### 4. Update Risk Matrix
- Modify existing risk matrix configurations
- Maintains business rule: only one active matrix per company
- Tracks update history

### 5. Delete Risk Matrix
- Soft delete functionality
- Maintains data integrity

## API Endpoints

### POST `/risk-matrix-master`
Create a new risk matrix

**Request Body:**
```json
{
  "matrixCode": "RM001",
  "matrixName": "Standard Risk Matrix",
  "rows": 5,
  "columns": 5,
  "status": "active", // optional, defaults to "active"
  "cells": [ // optional
    {
      "riskValueMappingId": "uuid-of-value-mapping",
      "cellPositions": ["r1c1", "r2c2", "r3c3"],
      "riskLevel": "low",
      "backgroundColor": "#00FF00",
      "description": "Low risk cells for specific value mapping"
    }
  ],
  "valueMappings": [ // optional
    {
      "valueKey": "text",
      "valueLabel": "Text Value",
      "sortOrder": 1,
      "color": "#0000FF",
      "description": "Text mapping"
    }
  ],
  "levelMappings": [ // optional
    {
      "riskLevels": ["low", "medium"],
      "levelLabel": "Low to Medium Risk",
      "minValue": 0,
      "maxValue": 5,
      "color": "#FFFF00",
      "sortOrder": 1,
      "description": "Combined low to medium risk level"
    }
  ]
}
```

### GET `/risk-matrix-master`
List risk matrices with pagination and filtering

**Query Parameters:**
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 20)
- `status`: Filter by status (active/inactive)
- `content`: Search in rows/columns
- `sort`: Sort fields (e.g., "rows:1;columns:-1")
- `createdAtFrom`: Date range filter start
- `createdAtTo`: Date range filter end

### GET `/risk-matrix-master/:id`
Get risk matrix details by ID

### PUT `/risk-matrix-master/:id`
Update an existing risk matrix

**Request Body:**
```json
{
  "rows": 6,
  "columns": 4,
  "status": "inactive"
}
```

### DELETE `/risk-matrix-master/:id`
Delete a risk matrix (soft delete)

### GET `/risk-matrix-master/:id/configuration`
Get complete matrix configuration including cells, value mappings, and level mappings

### POST `/risk-matrix-master/configure-cells`
Configure matrix cells for a specific risk matrix

**Request Body:**
```json
{
  "riskMatrixId": "uuid",
  "cells": [
    {
      "riskValueMappingId": "uuid-of-value-mapping",
      "cellPositions": ["r1c1", "r2c2"],
      "riskLevel": "low",
      "backgroundColor": "#00FF00",
      "description": "Low risk cells for text value"
    }
  ]
}
```

### POST `/risk-matrix-master/configure-value-mappings`
Configure value mappings for a specific risk matrix

**Request Body:**
```json
{
  "riskMatrixId": "uuid",
  "valueMappings": [
    {
      "valueKey": "text",
      "valueLabel": "Text Value",
      "sortOrder": 1,
      "color": "#0000FF",
      "description": "Text mapping"
    }
  ]
}
```

### POST `/risk-matrix-master/configure-level-mappings`
Configure level mappings for a specific risk matrix

**Request Body:**
```json
{
  "riskMatrixId": "uuid",
  "levelMappings": [
    {
      "riskLevels": ["negligible", "low"],
      "levelLabel": "Negligible to Low Risk",
      "minValue": 0,
      "maxValue": 3,
      "color": "#90EE90",
      "sortOrder": 1,
      "description": "Combined negligible to low risk level"
    }
  ]
}
```

## Validation Rules

1. **Matrix Code**: Must be unique per company, max 50 characters
2. **Matrix Name**: Optional, max 255 characters
3. **Rows**: Must be a positive integer (minimum 1)
4. **Columns**: Must be a positive integer (minimum 1)
5. **Status**: Must be either "active" or "inactive"
6. **Cell Values**: Max 100 characters per cell
7. **Risk Level**: Must be one of: negligible, low, medium, high
8. **Colors**: Must be valid hex color codes (7 characters including #)
9. **Business Rule**: Only one risk matrix can be active per company at any time

## Database Schema

### `RiskMatrixMaster` Entity
- `id`: UUID primary key
- `matrixCode`: Unique matrix code per company (varchar 50)
- `matrixName`: Optional matrix name (varchar 255)
- `rows`: Number of rows (integer)
- `columns`: Number of columns (integer)
- `status`: Status enum (active/inactive)
- `companyId`: Company UUID
- `createdUserId`: Creator user UUID
- `updatedUserId`: Updater user UUID
- Standard audit fields from `IdentifyEntity`

### `RiskMatrixCell` Entity
- `id`: UUID primary key
- `riskMatrixId`: Foreign key to RiskMatrixMaster
- `riskValueMappingId`: Foreign key to RiskValueMapping (nullable)
- `cellPositions`: Array of cell positions like ['r1c1', 'r2c2'] (simple-array)
- `riskLevel`: Risk level enum (negligible/low/medium/high)
- `backgroundColor`: Hex color code (varchar 7)
- `description`: Cell description (text)
- Standard audit fields from `IdentifyEntity`

### `RiskValueMapping` Entity
- `id`: UUID primary key
- `riskMatrixId`: Foreign key to RiskMatrixMaster
- `valueKey`: Unique value key per matrix (varchar 100)
- `valueLabel`: Display label (varchar 255)
- `sortOrder`: Sort order (integer)
- `color`: Hex color code (varchar 7)
- `description`: Value description (text)
- Standard audit fields from `IdentifyEntity`

### `RiskLevelMapping` Entity
- `id`: UUID primary key
- `riskMatrixId`: Foreign key to RiskMatrixMaster
- `riskLevels`: Array of risk levels for multiple selection (simple-array)
- `levelLabel`: Display label for the level combination (varchar 255)
- `minValue`: Minimum value range (integer)
- `maxValue`: Maximum value range (integer)
- `color`: Hex color code (varchar 7)
- `sortOrder`: Sort order (integer, default 0)
- `description`: Level description (text)
- Standard audit fields from `IdentifyEntity`

## Permissions

The module uses the following permission structure:
- Feature: `Configuration::Common::Risk Matrix Master`
- Actions: CREATE, VIEW, UPDATE, DELETE

## Error Handling

- Validates positive integers for rows and columns
- Prevents multiple active matrices per company
- Returns appropriate HTTP status codes and error messages
- Handles not found scenarios gracefully

## Usage Example

```typescript
// Create a new risk matrix
const newMatrix = await riskMatrixService.createRiskMatrix({
  rows: 5,
  columns: 5,
  status: 'active'
}, token);

// List matrices with filtering
const matrices = await riskMatrixService.listRiskMatrix({
  page: 1,
  pageSize: 10,
  status: 'active'
}, token);
``` 