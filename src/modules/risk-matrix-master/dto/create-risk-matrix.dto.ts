import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt, Min, IsEnum, IsOptional, IsString, MaxLength, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

import { StatusCommon } from '../../../commons/enums';
import { RiskLevelEnum } from '../risk-level-mapping.entity';

export class CreateRiskMatrixCellDTO {
  @ApiProperty({ description: 'Risk value mapping ID', required: false })
  @IsOptional()
  @IsString()
  riskValueMappingId?: string;

  @ApiProperty({
    type: [String],
    description: 'Array of cell positions like ["r1c1", "r2c2", "r3c3"]',
    example: ['r1c1', 'r2c2', 'r3c3']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  cellPositions?: string[];


  // @ApiProperty({ enum: RiskLevelEnum, description: 'Risk level', required: false })
  // @IsOptional()
  // @IsEnum(RiskLevelEnum)
  // riskLevel?: RiskLevelEnum;

  // @ApiProperty({ description: 'Background color (hex)', required: false })
  // @IsOptional()
  // @IsString()
  // @MaxLength(7)
  // backgroundColor?: string;

  // @ApiProperty({ description: 'Cell description', required: false })
  // @IsOptional()
  // @IsString()
  // description?: string;
}

export class CreateRiskValueMappingDTO {
  @ApiProperty({ description: 'Value key' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  valueKey: string;

  // @ApiProperty({ description: 'Value label' })
  // @IsNotEmpty()
  // @IsString()
  // @MaxLength(255)
  // valueLabel: string;

  // @ApiProperty({ description: 'Sort order', required: false })
  // @IsOptional()
  // @Type(() => Number)
  // @IsInt()
  // sortOrder?: number;

  @ApiProperty({ description: 'Color (hex)', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(7)
  color?: string;

  // @ApiProperty({ description: 'Description', required: false })
  // @IsOptional()
  // @IsString()
  // description?: string;
}

export class CreateRiskLevelMappingDTO {
  @ApiProperty({
    type: [String],
    description: 'Array of RiskValueMapping UUIDs for multiple selection',
    example: ['uuid-1', 'uuid-2', 'uuid-3']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  riskValueMappingIds?: string[];

  // @ApiProperty({ description: 'Level label' })
  // @IsNotEmpty()
  // @IsString()
  // @MaxLength(255)
  // levelLabel: string;

  // @ApiProperty({ description: 'Minimum value', required: false })
  // @IsOptional()
  // @Type(() => Number)
  // @IsInt()
  // minValue?: number;

  // @ApiProperty({ description: 'Maximum value', required: false })
  // @IsOptional()
  // @Type(() => Number)
  // @IsInt()
  // maxValue?: number;

  // @ApiProperty({ description: 'Color (hex)', required: false })
  // @IsOptional()
  // @IsString()
  // @MaxLength(7)
  // color?: string;

  // @ApiProperty({ description: 'Sort order', required: false })
  // @IsOptional()
  // @Type(() => Number)
  // @IsInt()
  // sortOrder?: number;

  // @ApiProperty({ description: 'Description', required: false })
  // @IsOptional()
  // @IsString()
  // description?: string;
}

export class CreateRiskMatrixDTO {
  @ApiProperty({ description: 'Matrix code' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  matrixCode: string;

  // @ApiProperty({ description: 'Matrix name', required: false })
  // @IsOptional()
  // @IsString()
  // @MaxLength(255)
  // matrixName?: string;

  @ApiProperty({
    type: Number,
    description: 'Number of rows in the risk matrix',
    minimum: 1,
  })
  @IsNotEmpty()
  @Type(() => Number)
  @IsInt()
  @Min(1, { message: 'Rows must be a positive integer' })
  rows: number;

  @ApiProperty({
    type: Number,
    description: 'Number of columns in the risk matrix',
    minimum: 1,
  })
  @IsNotEmpty()
  @Type(() => Number)
  @IsInt()
  @Min(1, { message: 'Columns must be a positive integer' })
  columns: number;

  @ApiProperty({
    type: [String],
    description: 'Array of row names like ["Probability", "Likelihood"]',
    example: ['Probability', 'Likelihood'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  rowsName?: string[];

  @ApiProperty({
    type: [String],
    description: 'Array of column names like ["Impact", "Severity"]',
    example: ['Impact', 'Severity'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  columnsName?: string[];

  @ApiProperty({
    enum: StatusCommon,
    description: 'Status of the risk matrix',
    required: false,
    default: StatusCommon.ACTIVE,
  })
  @IsOptional()
  @IsEnum(StatusCommon)
  status?: string;

  @ApiProperty({ type: [CreateRiskMatrixCellDTO], description: 'Matrix cells', required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRiskMatrixCellDTO)
  cells?: CreateRiskMatrixCellDTO[];

  @ApiProperty({ type: [CreateRiskValueMappingDTO], description: 'Value mappings', required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRiskValueMappingDTO)
  valueMappings?: CreateRiskValueMappingDTO[];

  @ApiProperty({ type: [CreateRiskLevelMappingDTO], description: 'Level mappings', required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRiskLevelMappingDTO)
  levelMappings?: CreateRiskLevelMappingDTO[];
}