import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { RiskLevelMapping } from '../risk-matrix-master/risk-level-mapping.entity';

@Entity()
export class PriorityMaster {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ nullable: false })
  public risk: string;

  @Column({ type: 'smallint', nullable: false })
  public order: number;

  @Column({ type: 'smallint' })
  public value: number;

  @Column({ nullable: true })
  public description: string;

  @OneToMany(() => RiskLevelMapping, (riskLevelMapping) => riskLevelMapping.priorityMaster, { onDelete: 'CASCADE' })
  riskLevelMapping: RiskLevelMapping[];
}
